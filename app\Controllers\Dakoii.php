<?php

namespace App\Controllers;

use App\Models\AdxCountryModel;
use App\Models\DakoiiUsersModel;
use App\Models\DakoiiOrgModel;
use App\Models\AdxProvinceModel;
use App\Models\UsersModel;

class <PERSON>koii extends BaseController
{
    public $session;
    public $dusersModel;
    public $usersModel;
    public $orgModel;
    public $countryModel;
    public $provinceModel;
    public $districtModel;
    public $llgModel;
    public $wardModel;

    public $cropsModel;
    public $fertilizersModel;
    public $pesticidesModel;
    public $educationModel;
    public $infectionsModel;
    public $livestockModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();

        // Initialize models
        $this->dusersModel = new DakoiiUsersModel();
        $this->usersModel = new UsersModel();
        $this->orgModel = new DakoiiOrgModel();
        $this->countryModel = new AdxCountryModel();
        $this->provinceModel = new AdxProvinceModel();
        $this->districtModel = new \App\Models\AdxDistrictModel();
        $this->llgModel = new \App\Models\AdxLlgModel();
        $this->wardModel = new \App\Models\AdxWardModel();

        $this->cropsModel = new \App\Models\CropsModel();
        $this->fertilizersModel = new \App\Models\FertilizersModel();
        $this->pesticidesModel = new \App\Models\PesticidesModel();
        $this->educationModel = new \App\Models\EducationModel();
        $this->infectionsModel = new \App\Models\InfectionsModel();
        $this->livestockModel = new \App\Models\LivestockModel();

        // Ensure session isolation for Dakoii portal
        $this->ensureSessionIsolation();
    }

    /**
     * Ensure session isolation between main app and Dakoii portal
     */
    private function ensureSessionIsolation(): void
    {
        // If user is accessing Dakoii portal but has main app session without Dakoii session,
        // clear main app session to prevent conflicts
        $currentUri = uri_string();

        if (strpos($currentUri, 'dakoii') !== false) {
            // User is accessing Dakoii portal
            if ($this->session->has('logged_in') && !$this->session->has('dakoii_logged_in')) {
                // Has main app session but no Dakoii session - clear main app session
                $mainAppSessionKeys = ['logged_in', 'name', 'role', 'emp_id', 'org_id', 'fileno', 'position', 'status'];
                foreach ($mainAppSessionKeys as $key) {
                    $this->session->remove($key);
                }
                log_message('info', 'Dakoii Portal: Cleared conflicting main app session for session isolation');
            }
        }
    }

    // Authentication Methods - REMOVED
    // All authentication is now handled by DakoiiAuth controller
    // This ensures proper RESTful approach with separate GET/POST methods

    // Dashboard Methods
    public function ddash()
    {
        // Additional session check (filter should handle this, but double-check for security)
        if (!$this->session->has('dakoii_logged_in') || !$this->session->get('dakoii_logged_in')) {
            session()->setFlashdata('error', 'Session expired. Please login again.');
            return redirect()->to(base_url('dakoii/login'));
        }

        $data['title'] = "Dakoii Admin Dashboard";
        $data['menu'] = "ddash";

        // Get current user information
        $data['current_user'] = [
            'id' => $this->session->get('dakoii_user_id'),
            'name' => $this->session->get('dakoii_name'),
            'username' => $this->session->get('dakoii_username'),
            'role' => $this->session->get('dakoii_role'),
            'orgcode' => $this->session->get('dakoii_orgcode'),
            'login_time' => $this->session->get('dakoii_login_time'),
            'login_datetime' => $this->session->get('dakoii_login_datetime'),
            'ip_address' => $this->session->get('dakoii_ip_address')
        ];

        // Get user statistics for dashboard
        $data['user_stats'] = $this->dusersModel->getUserStats();

        // Get recent users (last 10 created)
        $data['recent_users'] = $this->dusersModel->orderBy('created_at', 'DESC')->findAll(10);

        // Get active users by role
        $data['users_by_role'] = [
            'super_admin' => $this->dusersModel->getActiveUsersByRole('super_admin'),
            'admin' => $this->dusersModel->getActiveUsersByRole('admin'),
            'dakoii' => $this->dusersModel->getActiveUsersByRole('dakoii')
        ];

        // Get organization statistics (if organizations model exists)
        try {
            $data['org_stats'] = [
                'total_organizations' => $this->organizationsModel->countAllResults(),
                'active_organizations' => $this->organizationsModel->where('is_active', 1)->countAllResults(),
                'recent_organizations' => $this->organizationsModel->orderBy('created_at', 'DESC')->findAll(5)
            ];
        } catch (\Exception $e) {
            // If organizations model doesn't exist or has issues, set default values
            $data['org_stats'] = [
                'total_organizations' => 0,
                'active_organizations' => 0,
                'recent_organizations' => []
            ];
        }

        // Calculate dashboard metrics
        $data['dashboard_metrics'] = [
            'total_users' => $data['user_stats']['total_users'],
            'active_users_percentage' => $data['user_stats']['total_users'] > 0 ?
                round(($data['user_stats']['active_users'] / $data['user_stats']['total_users']) * 100, 1) : 0,
            'admin_users_percentage' => $data['user_stats']['total_users'] > 0 ?
                round((($data['user_stats']['admin_users'] + $data['user_stats']['super_admin_users']) / $data['user_stats']['total_users']) * 100, 1) : 0,
            'system_status' => 'operational', // You can implement actual system checks here
            'last_backup' => date('Y-m-d H:i:s'), // Placeholder - implement actual backup tracking
        ];

        $data['dusers'] = $this->dusersModel->findAll();
        $data['admins'] = $this->usersModel->findAll();
        $data['org'] = $this->orgModel->orderBy('id', 'DESC')->findAll();
        // Note: selections table no longer exists - data moved to dedicated tables

        $data['provinces_count'] = $this->provinceModel->countAllResults();
        $data['districts_count'] = $this->districtModel->countAllResults();
        $data['llgs_count'] = $this->llgModel->countAllResults();
        $data['wards_count'] = $this->wardModel->countAllResults();

        $data['province_stats'] = $this->getProvinceStats();

        $data['crops'] = $this->cropsModel->findAll();
        $data['fertilizers'] = $this->fertilizersModel->findAll();
        $data['pesticides'] = $this->pesticidesModel->findAll();
        $data['education'] = $this->educationModel->findAll();
        $data['infections'] = $this->infectionsModel->findAll();
        $data['livestock'] = $this->livestockModel->findAll();
        
        echo view('dakoii/ddash', $data);
    }

    private function getProvinceStats()
    {
        $provinces = $this->provinceModel->findAll();
        $stats = [];

        foreach ($provinces as $province) {
            $districts = $this->districtModel->where('province_id', $province['id'])->findAll();
            $district_ids = array_column($districts, 'id');
            
            $llgs_count = 0;
            $wards_count = 0;
            
            if (!empty($district_ids)) {
                $llgs_count = $this->llgModel->whereIn('district_id', $district_ids)->countAllResults();
                $llgs = $this->llgModel->whereIn('district_id', $district_ids)->findAll();
                $llg_ids = array_column($llgs, 'id');
                
                if (!empty($llg_ids)) {
                    $wards_count = $this->wardModel->whereIn('llg_id', $llg_ids)->countAllResults();
                }
            }

            $stats[$province['id']] = [
                'name' => $province['name'],
                'districts' => count($districts),
                'llgs' => $llgs_count,
                'wards' => $wards_count
            ];
        }

        return $stats;
    }



    // Note: User Management Methods have been moved to DakoiiUsers controller
    // to follow RESTful approach with separate GET and POST methods

    // Province Management Methods
    public function provinces()
    {
        $data['title'] = "Provinces";
        $data['menu'] = "provinces";
        
        $data['set_country'] = $this->countryModel->where('code', COUNTRY_CODE)->first();
        $data['provinces'] = $this->provinceModel
            ->where('country_id', $data['set_country']['id'])
            ->orderBy('name', 'asc')
            ->findAll();
        
        echo view('dakoii/provinces', $data);
    }

    public function addProvince()
    {
        if ($this->request->getMethod() === 'post') {
            $rules = [
                'name' => 'required|min_length[3]|max_length[100]',
                'provincecode' => 'required|is_unique[adx_province.provincecode]'
            ];

            if ($this->validate($rules)) {
                $data = [
                    'name' => $this->request->getPost('name'),
                    'provincecode' => $this->request->getPost('provincecode'),
                    'country_id' => $this->request->getPost('country_id'),
                    'json_id' => $this->request->getPost('json_id')
                ];

                if ($this->provinceModel->insert($data)) {
                    session()->setFlashdata('success', 'Province "' . $data['name'] . '" has been added successfully');
                } else {
                    session()->setFlashdata('error', 'Failed to add province. Please try again.');
                }
            } else {
                session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
            }
        }
        return redirect()->to('location-management/provinces');
    }

    public function editProvince()
    {
        if ($this->request->getMethod() === 'post') {
            $id = $this->request->getPost('id');
            
            $rules = [
                'name' => 'required|min_length[3]|max_length[100]',
                'provincecode' => 'required|is_unique[adx_province.provincecode,id,' . $id . ']'
            ];

            if ($this->validate($rules)) {
                $data = [
                    'name' => $this->request->getPost('name'),
                    'provincecode' => $this->request->getPost('provincecode'),
                    'json_id' => $this->request->getPost('json_id')
                ];

                if ($this->provinceModel->update($id, $data)) {
                    session()->setFlashdata('success', 'Province updated successfully');
                } else {
                    session()->setFlashdata('error', 'Failed to update province');
                }
            } else {
                session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
            }
        }
        return redirect()->to('location-management/provinces');
    }

    public function deleteProvince($id)
    {
        if ($this->provinceModel->delete($id)) {
            session()->setFlashdata('success', 'Province deleted successfully');
        } else {
            session()->setFlashdata('error', 'Failed to delete province');
        }
        return redirect()->to('location-management/provinces');
    }

    public function getProvince($id)
    {
        $province = $this->provinceModel->find($id);
        return $this->response->setJSON($province);
    }

    // District Management Methods
    public function districts($provinceId)
    {
        $data['title'] = "Districts";
        $data['menu'] = "provinces";
        
        $data['province'] = $this->provinceModel->find($provinceId);
        if (!$data['province']) {
            session()->setFlashdata('error', 'Province not found');
            return redirect()->to('location-management/provinces');
        }
        
        $data['districts'] = $this->districtModel
            ->where('province_id', $provinceId)
            ->orderBy('name', 'asc')
            ->findAll();
        
        echo view('dakoii/districts', $data);
    }

    public function getDistricts($provinceId)
    {
        $districts = $this->districtModel
            ->where('province_id', $provinceId)
            ->orderBy('name', 'asc')
            ->findAll();
        
        return $this->response->setJSON($districts);
    }

    public function addDistrict()
    {
        if ($this->request->getMethod() === 'post') {
            $rules = [
                'name' => 'required|min_length[3]|max_length[100]',
                'districtcode' => 'required|is_unique[adx_district.districtcode]',
                'province_id' => 'required|numeric',
                'country_id' => 'required|numeric'
            ];

            if ($this->validate($rules)) {
                $data = [
                    'name' => $this->request->getPost('name'),
                    'districtcode' => $this->request->getPost('districtcode'),
                    'province_id' => $this->request->getPost('province_id'),
                    'country_id' => $this->request->getPost('country_id'),
                    'json_id' => $this->request->getPost('json_id')
                ];

                if ($this->districtModel->insert($data)) {
                    session()->setFlashdata('success', 'District "' . $data['name'] . '" has been added successfully');
                } else {
                    session()->setFlashdata('error', 'Failed to add district. Please try again.');
                }
            } else {
                session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
            }
        }
        return redirect()->back();
    }

    public function editDistrict()
    {
        if ($this->request->getMethod() === 'post') {
            $id = $this->request->getPost('id');
            
            $rules = [
                'name' => 'required|min_length[3]|max_length[100]',
                'districtcode' => 'required|is_unique[adx_district.districtcode,id,' . $id . ']'
            ];

            if ($this->validate($rules)) {
                $data = [
                    'name' => $this->request->getPost('name'),
                    'districtcode' => $this->request->getPost('districtcode'),
                    'json_id' => $this->request->getPost('json_id')
                ];

                if ($this->districtModel->update($id, $data)) {
                    session()->setFlashdata('success', 'District updated successfully');
                } else {
                    session()->setFlashdata('error', 'Failed to update district');
                }
            } else {
                session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
            }
        }
        return redirect()->back();
    }

    public function deleteDistrict($id)
    {
        $district = $this->districtModel->find($id);
        if ($district) {
            if ($this->districtModel->delete($id)) {
                session()->setFlashdata('success', 'District "' . $district['name'] . '" has been deleted successfully');
            } else {
                session()->setFlashdata('error', 'Failed to delete district. Please try again.');
            }
        } else {
            session()->setFlashdata('error', 'District not found');
        }
        return redirect()->back();
    }

    // LLG Management Methods
    public function llgs($districtId)
    {
        $data['title'] = "LLGs";
        $data['menu'] = "provinces";
        
        $data['district'] = $this->districtModel->find($districtId);
        if (!$data['district']) {
            session()->setFlashdata('error', 'District not found');
            return redirect()->to('location-management/provinces');
        }
        
        $data['province'] = $this->provinceModel->find($data['district']['province_id']);
        $data['llgs'] = $this->llgModel
            ->where('district_id', $districtId)
            ->orderBy('name', 'asc')
            ->findAll();
        
        echo view('dakoii/llgs', $data);
    }

    public function addLLG()
    {
        if ($this->request->getMethod() === 'post') {
            $rules = [
                'name' => 'required|min_length[3]|max_length[100]',
                'llgcode' => 'required|is_unique[adx_llg.llgcode]',
                'district_id' => 'required|numeric',
                'province_id' => 'required|numeric',
                'country_id' => 'required|numeric'
            ];

            if ($this->validate($rules)) {
                $data = [
                    'name' => $this->request->getPost('name'),
                    'llgcode' => $this->request->getPost('llgcode'),
                    'district_id' => $this->request->getPost('district_id'),
                    'province_id' => $this->request->getPost('province_id'),
                    'country_id' => $this->request->getPost('country_id'),
                    'json_id' => $this->request->getPost('json_id')
                ];

                if ($this->llgModel->insert($data)) {
                    session()->setFlashdata('success', 'LLG "' . $data['name'] . '" has been added successfully');
                } else {
                    session()->setFlashdata('error', 'Failed to add LLG. Please try again.');
                }
            } else {
                session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
            }
        }
        return redirect()->back();
    }

    public function editLLG()
    {
        if ($this->request->getMethod() === 'post') {
            $id = $this->request->getPost('id');
            
            $rules = [
                'name' => 'required|min_length[3]|max_length[100]',
                'llgcode' => 'required|is_unique[adx_llg.llgcode,id,' . $id . ']'
            ];

            if ($this->validate($rules)) {
                $data = [
                    'name' => $this->request->getPost('name'),
                    'llgcode' => $this->request->getPost('llgcode'),
                    'json_id' => $this->request->getPost('json_id')
                ];

                if ($this->llgModel->update($id, $data)) {
                    session()->setFlashdata('success', 'LLG updated successfully');
                } else {
                    session()->setFlashdata('error', 'Failed to update LLG');
                }
            } else {
                session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
            }
        }
        return redirect()->back();
    }

    public function deleteLLG($id)
    {
        $llg = $this->llgModel->find($id);
        if ($llg) {
            if ($this->llgModel->delete($id)) {
                session()->setFlashdata('success', 'LLG "' . $llg['name'] . '" has been deleted successfully');
            } else {
                session()->setFlashdata('error', 'Failed to delete LLG. Please try again.');
            }
        } else {
            session()->setFlashdata('error', 'LLG not found');
        }
        return redirect()->back();
    }

    // Ward Management Methods
    public function wards($llgId)
    {
        $data['title'] = "Wards";
        $data['menu'] = "provinces";
        
        $data['llg'] = $this->llgModel->find($llgId);
        if (!$data['llg']) {
            session()->setFlashdata('error', 'LLG not found');
            return redirect()->to('location-management/provinces');
        }
        
        $data['district'] = $this->districtModel->find($data['llg']['district_id']);
        $data['province'] = $this->provinceModel->find($data['district']['province_id']);
        
        $data['wards'] = $this->wardModel
            ->where('llg_id', $llgId)
            ->orderBy('name', 'asc')
            ->findAll();
        
        echo view('dakoii/wards', $data);
    }

    public function addWard()
    {
        if ($this->request->getMethod() === 'post') {
            $rules = [
                'name' => 'required|min_length[3]|max_length[100]',
                'wardcode' => 'required|is_unique[adx_ward.wardcode]',
                'llg_id' => 'required|numeric',
                'district_id' => 'required|numeric',
                'province_id' => 'required|numeric',
                'country_id' => 'required|numeric'
            ];

            if ($this->validate($rules)) {
                $data = [
                    'name' => $this->request->getPost('name'),
                    'wardcode' => $this->request->getPost('wardcode'),
                    'llg_id' => $this->request->getPost('llg_id'),
                    'district_id' => $this->request->getPost('district_id'),
                    'province_id' => $this->request->getPost('province_id'),
                    'country_id' => $this->request->getPost('country_id')
                ];

                if ($this->wardModel->insert($data)) {
                    session()->setFlashdata('success', 'Ward "' . $data['name'] . '" has been added successfully');
                } else {
                    session()->setFlashdata('error', 'Failed to add ward. Please try again.');
                }
            } else {
                session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
            }
        }
        return redirect()->back();
    }

    public function editWard()
    {
        if ($this->request->getMethod() === 'post') {
            $id = $this->request->getPost('id');
            
            $rules = [
                'name' => 'required|min_length[3]|max_length[100]',
                'wardcode' => 'required|is_unique[adx_ward.wardcode,id,' . $id . ']'
            ];

            if ($this->validate($rules)) {
                $data = [
                    'name' => $this->request->getPost('name'),
                    'wardcode' => $this->request->getPost('wardcode')
                ];

                if ($this->wardModel->update($id, $data)) {
                    session()->setFlashdata('success', 'Ward updated successfully');
                } else {
                    session()->setFlashdata('error', 'Failed to update ward');
                }
            } else {
                session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
            }
        }
        return redirect()->back();
    }

    public function deleteWard($id)
    {
        $ward = $this->wardModel->find($id);
        if ($ward) {
            if ($this->wardModel->delete($id)) {
                session()->setFlashdata('success', 'Ward "' . $ward['name'] . '" has been deleted successfully');
            } else {
                session()->setFlashdata('error', 'Failed to delete ward. Please try again.');
            }
        } else {
            session()->setFlashdata('error', 'Ward not found');
        }
        return redirect()->back();
    }

    // Note: Crop management methods have been moved to DakoiiData controller
    // to follow RESTful approach with separate GET and POST methods

    // Note: Fertilizer management methods have been moved to DakoiiData controller
    // to follow RESTful approach with separate GET and POST methods

    // Note: Pesticide management methods have been moved to DakoiiData controller
    // to follow RESTful approach with separate GET and POST methods

    // Note: Education management methods have been moved to DakoiiData controller
    // to follow RESTful approach with separate GET and POST methods

    // Note: Infection management methods have been moved to DakoiiData controller
    // to follow RESTful approach with separate GET and POST methods

    // Note: Livestock management methods have been moved to DakoiiData controller
    // to follow RESTful approach with separate GET and POST methods
}
