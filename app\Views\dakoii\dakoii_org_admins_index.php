<?= $this->extend('templates/dakoii_template') ?>

<?= $this->section('content') ?>

<!-- <PERSON> Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0"><?= esc($title) ?></h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="<?= base_url('dakoii/organizations') ?>">Organizations</a></li>
                <li class="breadcrumb-item"><a href="<?= base_url('dakoii/organizations/show/' . $org['id']) ?>"><?= esc($org['name']) ?></a></li>
                <li class="breadcrumb-item active">Administrators</li>
            </ol>
        </nav>
    </div>
    <div class="btn-group">
        <a href="<?= base_url('dakoii/organizations/show/' . $org['id']) ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Organization
        </a>
        <a href="<?= base_url('dakoii/organizations/' . $org['id'] . '/admins/create') ?>" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add Administrator
        </a>
    </div>
</div>

<!-- Organization Info -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-2">
                <?php if (!empty($org['orglogo'])): ?>
                    <img src="<?= esc($org['orglogo']) ?>" alt="Logo" class="img-fluid rounded" style="max-height: 80px;">
                <?php else: ?>
                    <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 80px;">
                        <i class="fas fa-building fa-2x text-muted"></i>
                    </div>
                <?php endif; ?>
            </div>
            <div class="col-md-10">
                <h5 class="mb-1"><?= esc($org['name']) ?></h5>
                <p class="text-muted mb-1"><?= esc($org['description']) ?></p>
                <div class="d-flex gap-2">
                    <span class="badge bg-<?= $org['is_active'] ? 'success' : 'danger' ?>">
                        <?= $org['is_active'] ? 'Active' : 'Inactive' ?>
                    </span>
                    <span class="badge bg-info"><?= ucfirst(esc($org['license_status'])) ?></span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Administrators List -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-user-shield"></i> Administrators (<?= count($admins) ?>)
        </h5>
    </div>
    <div class="card-body">
        <?php if (!empty($admins)): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Name</th>
                            <th>Username</th>
                            <th>Position</th>
                            <th>Contact</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($admins as $admin): ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <?php if (!empty($admin['id_photo'])): ?>
                                        <img src="<?= imgcheck($admin['id_photo']) ?>" alt="Photo" 
                                             class="rounded-circle me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                    <?php else: ?>
                                        <div class="bg-secondary rounded-circle me-2 d-flex align-items-center justify-content-center" 
                                             style="width: 40px; height: 40px;">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                    <?php endif; ?>
                                    <div>
                                        <div class="fw-medium"><?= esc($admin['name']) ?></div>
                                        <?php if (!empty($admin['fileno'])): ?>
                                            <small class="text-muted">File: <?= esc($admin['fileno']) ?></small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="font-monospace"><?= esc($admin['username']) ?></span>
                            </td>
                            <td>
                                <?= !empty($admin['position']) ? esc($admin['position']) : '<span class="text-muted">Not specified</span>' ?>
                            </td>
                            <td>
                                <?php if (!empty($admin['phone']) || !empty($admin['email'])): ?>
                                    <?php if (!empty($admin['phone'])): ?>
                                        <div><i class="fas fa-phone fa-sm text-muted"></i> <?= esc($admin['phone']) ?></div>
                                    <?php endif; ?>
                                    <?php if (!empty($admin['email'])): ?>
                                        <div><i class="fas fa-envelope fa-sm text-muted"></i> <?= esc($admin['email']) ?></div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="text-muted">No contact info</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge bg-<?= $admin['status'] ? 'success' : 'danger' ?>">
                                    <?= $admin['status'] ? 'Active' : 'Inactive' ?>
                                </span>
                            </td>
                            <td>
                                <div><?= date('M j, Y', strtotime($admin['created_at'])) ?></div>
                                <small class="text-muted"><?= date('g:i A', strtotime($admin['created_at'])) ?></small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="<?= base_url('dakoii/system-admins/show/' . $admin['id']) ?>" 
                                       class="btn btn-sm btn-outline-info" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?= base_url('dakoii/system-admins/edit/' . $admin['id']) ?>" 
                                       class="btn btn-sm btn-outline-primary" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="<?= base_url('dakoii/system-admins/delete/' . $admin['id']) ?>" method="post" class="d-inline">
                                        <?= csrf_field() ?>
                                        <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                onclick="return confirm('Are you sure you want to delete this administrator?')" 
                                                title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-user-shield fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Administrators Found</h5>
                <p class="text-muted">This organization doesn't have any administrators yet.</p>
                <a href="<?= base_url('dakoii/organizations/' . $org['id'] . '/admins/create') ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add First Administrator
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<?= $this->endSection() ?>
