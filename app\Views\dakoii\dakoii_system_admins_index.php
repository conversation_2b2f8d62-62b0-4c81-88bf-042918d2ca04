<?= $this->extend("templates/dakoii_template"); ?>
<?= $this->section('content'); ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>">Dashboard</a></li>
        <li class="breadcrumb-item active">System Administrators</li>
    </ol>
</nav>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-0">System Administrators</h2>
        <p class="text-muted mb-0">Manage organization administrators from the main users table</p>
    </div>
    <a href="<?= base_url('dakoii/system-admins/create') ?>" class="btn btn-primary">
        <i class="fas fa-plus"></i> Add System Administrator
    </a>
</div>

<!-- Flash Messages -->
<?php if (session()->getFlashdata('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle"></i> <?= session()->getFlashdata('success') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle"></i> <?= session()->getFlashdata('error') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- System Administrators Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-user-shield"></i> System Administrators List
        </h5>
    </div>
    <div class="card-body">
        <?php if (!empty($admins)): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Name</th>
                            <th>Username</th>
                            <th>Organization</th>
                            <th>Position</th>
                            <th>Contact</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($admins as $admin): ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <?php if (!empty($admin['id_photo'])): ?>
                                        <img src="<?= imgcheck($admin['id_photo']) ?>" alt="Photo"
                                             class="rounded-circle me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                    <?php else: ?>
                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2"
                                             style="width: 40px; height: 40px;">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                    <?php endif; ?>
                                    <div>
                                        <div class="fw-medium"><?= esc($admin['name']) ?></div>
                                        <?php if (!empty($admin['fileno'])): ?>
                                            <small class="text-muted">File: <?= esc($admin['fileno']) ?></small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <code class="bg-light px-2 py-1 rounded"><?= esc($admin['username']) ?></code>
                            </td>
                            <td>
                                <div>
                                    <div class="fw-medium"><?= esc($admin['org_name'] ?? 'Unknown') ?></div>
                                    <small class="text-muted">Code: <?= esc($admin['orgcode']) ?></small>
                                </div>
                            </td>
                            <td>
                                <?= esc($admin['position']) ?: '<em class="text-muted">Not specified</em>' ?>
                            </td>
                            <td>
                                <?php if (!empty($admin['email'])): ?>
                                    <div><i class="fas fa-envelope text-muted"></i> <?= esc($admin['email']) ?></div>
                                <?php endif; ?>
                                <?php if (!empty($admin['phone'])): ?>
                                    <div><i class="fas fa-phone text-muted"></i> <?= esc($admin['phone']) ?></div>
                                <?php endif; ?>
                                <?php if (empty($admin['email']) && empty($admin['phone'])): ?>
                                    <em class="text-muted">No contact info</em>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge bg-<?= $admin['status'] ? 'success' : 'danger' ?> fs-6">
                                    <i class="fas fa-<?= $admin['status'] ? 'check' : 'times' ?>"></i>
                                    <?= $admin['status'] ? 'Active' : 'Inactive' ?>
                                </span>
                            </td>
                            <td>
                                <div><?= date('M j, Y', strtotime($admin['created_at'])) ?></div>
                                <small class="text-muted"><?= date('g:i A', strtotime($admin['created_at'])) ?></small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="<?= base_url('dakoii/system-admins/show/' . $admin['id']) ?>"
                                       class="btn btn-sm btn-outline-info" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?= base_url('dakoii/system-admins/edit/' . $admin['id']) ?>"
                                       class="btn btn-sm btn-outline-primary" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                            onclick="confirmDelete(<?= $admin['id'] ?>, '<?= esc($admin['name']) ?>')"
                                            title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-user-shield fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">No System Administrators Found</h5>
                <p class="text-muted">There are no system administrators in the database.</p>
                <a href="<?= base_url('dakoii/system-admins/create') ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add First System Administrator
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-warning"></i> Confirm Delete
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the system administrator <strong id="adminName"></strong>?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Warning:</strong> This action cannot be undone. The administrator will lose access to the system.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    <?= csrf_field() ?>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Delete Administrator
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(adminId, adminName) {
    document.getElementById('adminName').textContent = adminName;
    document.getElementById('deleteForm').action = '<?= base_url('dakoii/system-admins/delete/') ?>' + adminId;

    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>

<?= $this->endSection() ?>
