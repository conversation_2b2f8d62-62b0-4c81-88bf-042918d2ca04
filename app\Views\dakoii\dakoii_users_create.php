<?= $this->extend("templates/dakoii_template"); ?>
<?= $this->section('content'); ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/users') ?>">Users</a></li>
        <li class="breadcrumb-item active">Create User</li>
    </ol>
</nav>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-0">Create New User</h2>
        <p class="text-muted mb-0">Add a new system administrator or user</p>
    </div>
    <a href="<?= base_url('dakoii/users') ?>" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left"></i> Back to Users
    </a>
</div>

<!-- Create Form -->
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-plus"></i> User Information
                </h5>
            </div>
            <div class="card-body">
                <?= form_open('dakoii/users/store') ?>
                <?= csrf_field() ?>
                
                <div class="row">
                    <!-- Personal Information -->
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3">Personal Information</h6>
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="<?= old('name') ?>" required maxlength="255">
                            <div class="form-text">Enter the user's full name</div>
                        </div>

                        <div class="mb-3">
                            <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="username" name="username" 
                                   value="<?= old('username') ?>" required maxlength="255">
                            <div class="form-text">Unique username for login (minimum 3 characters)</div>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password" 
                                       required minlength="6">
                                <button type="button" class="btn btn-outline-secondary" id="togglePassword">
                                    <i class="fas fa-eye" id="toggleIcon"></i>
                                </button>
                            </div>
                            <div class="form-text">Minimum 6 characters required</div>
                        </div>

                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirm Password <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                   required minlength="6">
                            <div class="form-text">Re-enter the password to confirm</div>
                        </div>
                    </div>

                    <!-- Account Settings -->
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3">Account Settings</h6>
                        


                        <div class="mb-3">
                            <label for="role" class="form-label">User Role <span class="text-danger">*</span></label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="">Select Role</option>
                                <option value="user" <?= old('role') == 'user' ? 'selected' : '' ?>>User</option>
                                <option value="moderator" <?= old('role') == 'moderator' ? 'selected' : '' ?>>Moderator</option>
                                <option value="admin" <?= old('role') == 'admin' ? 'selected' : '' ?>>Administrator</option>
                            </select>
                            <div class="form-text">Define user's access level and permissions</div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                       value="1" <?= old('is_active') ? 'checked' : 'checked' ?>>
                                <label class="form-check-label" for="is_active">
                                    Active Account
                                </label>
                            </div>
                            <div class="form-text">Enable or disable user account access</div>
                        </div>

                        <!-- Role Descriptions -->
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Role Permissions:</strong>
                            <ul class="mb-0 mt-2">
                                <li><strong>User:</strong> Basic access to assigned features</li>
                                <li><strong>Moderator:</strong> Can manage data and users</li>
                                <li><strong>Administrator:</strong> Full system access</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="row mt-4">
                    <div class="col-12">
                        <hr>
                        <div class="d-flex justify-content-between">
                            <a href="<?= base_url('dakoii/users') ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-save"></i> Create User
                            </button>
                        </div>
                    </div>
                </div>

                <?= form_close() ?>
            </div>
        </div>
    </div>
</div>

<style>
.form-label {
    font-weight: 600;
}

.text-danger {
    color: #dc3545 !important;
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
}

.alert {
    border-radius: 10px;
}

.card {
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.form-check-input:checked {
    background-color: #198754;
    border-color: #198754;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password toggle functionality
    const togglePassword = document.getElementById('togglePassword');
    const passwordField = document.getElementById('password');
    const toggleIcon = document.getElementById('toggleIcon');

    togglePassword.addEventListener('click', function() {
        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    });

    // Form validation
    const form = document.querySelector('form');
    const submitBtn = document.getElementById('submitBtn');
    
    form.addEventListener('submit', function(e) {
        const name = document.getElementById('name').value.trim();
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirm_password').value;

        const role = document.getElementById('role').value;

        // Validate required fields
        if (name.length < 3) {
            e.preventDefault();
            alert('Full name must be at least 3 characters long');
            document.getElementById('name').focus();
            return;
        }

        if (username.length < 3) {
            e.preventDefault();
            alert('Username must be at least 3 characters long');
            document.getElementById('username').focus();
            return;
        }

        if (password.length < 6) {
            e.preventDefault();
            alert('Password must be at least 6 characters long');
            document.getElementById('password').focus();
            return;
        }

        if (password !== confirmPassword) {
            e.preventDefault();
            alert('Passwords do not match');
            document.getElementById('confirm_password').focus();
            return;
        }



        if (!role) {
            e.preventDefault();
            alert('Please select a user role');
            document.getElementById('role').focus();
            return;
        }

        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Creating...';
    });

    // Real-time password confirmation validation
    const confirmPasswordField = document.getElementById('confirm_password');
    confirmPasswordField.addEventListener('input', function() {
        const password = document.getElementById('password').value;
        const confirmPassword = this.value;
        
        if (confirmPassword && password !== confirmPassword) {
            this.classList.add('is-invalid');
        } else {
            this.classList.remove('is-invalid');
        }
    });

    // Auto-focus on name field
    document.getElementById('name').focus();
});
</script>

<?= $this->endSection() ?>
