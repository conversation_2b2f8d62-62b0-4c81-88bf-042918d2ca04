<?php

namespace App\Controllers;

use App\Models\DakoiiUsersModel;

/**
 * Dakoii Portal Users Controller
 *
 * Handles CRUD operations for Dakoii portal users (dakoii_users table)
 * This is separate from the main Users controller which handles organization staff
 */
class DakoiiPortalUsers extends BaseController
{
    public $session;
    public $portalUsersModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        $this->portalUsersModel = new DakoiiUsersModel();
    }

    /**
     * Display portal users list
     */
    public function index()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Portal User Management";
        $data['menu'] = "portal_users";
        $data['portal_users'] = $this->portalUsersModel->findAll();

        return view('dakoii/dakoii_portal_users_index', $data);
    }

    /**
     * Show create portal user form
     */
    public function create()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Create Portal User";
        $data['menu'] = "portal_users";

        return view('dakoii/dakoii_portal_users_create', $data);
    }

    /**
     * Store new portal user
     */
    public function store()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/portal-users');
        }

        // Validate input
        $rules = [
            'name' => 'required|min_length[3]|max_length[255]',
            'username' => 'required|min_length[3]|max_length[255]|is_unique[dakoii_users.username]',
            'password' => 'required|min_length[6]',
            'role' => 'required|in_list[user,moderator,admin]'
        ];

        if (!$this->validate($rules)) {
            session()->setFlashdata('error', 'Please enter valid portal user data. Username must be unique.');
            return redirect()->to('dakoii/portal-users/create')->withInput();
        }

        $data = [
            'name' => trim($this->request->getPost('name')),
            'username' => trim($this->request->getPost('username')),
            'password' => $this->request->getPost('password'), // Will be hashed by model
            'role' => $this->request->getPost('role'),
            'is_active' => $this->request->getPost('is_active') ? 1 : 0
        ];

        if ($this->portalUsersModel->createPortalUser($data)) {
            session()->setFlashdata('success', 'Portal user "' . $data['name'] . '" created successfully!');
            return redirect()->to('dakoii/portal-users');
        } else {
            session()->setFlashdata('error', 'Failed to create portal user. Username may already exist.');
            return redirect()->to('dakoii/portal-users/create')->withInput();
        }
    }

    /**
     * Show portal user details
     */
    public function show($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $portalUser = $this->portalUsersModel->getSafePortalUser($id);
        if (!$portalUser) {
            session()->setFlashdata('error', 'Portal user not found');
            return redirect()->to('dakoii/portal-users');
        }

        $data['title'] = "Portal User Details - " . $portalUser['name'];
        $data['menu'] = "portal_users";
        $data['portal_user'] = $portalUser;

        return view('dakoii/dakoii_portal_users_show', $data);
    }

    /**
     * Show edit portal user form
     */
    public function edit($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $portalUser = $this->portalUsersModel->find($id);
        if (!$portalUser) {
            session()->setFlashdata('error', 'Portal user not found');
            return redirect()->to('dakoii/portal-users');
        }

        $data['title'] = "Edit Portal User - " . $portalUser['name'];
        $data['menu'] = "portal_users";
        $data['portal_user'] = $portalUser;

        return view('dakoii/dakoii_portal_users_edit', $data);
    }

    /**
     * Update portal user
     */
    public function update($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/portal-users');
        }

        $portalUser = $this->portalUsersModel->find($id);
        if (!$portalUser) {
            session()->setFlashdata('error', 'Portal user not found');
            return redirect()->to('dakoii/portal-users');
        }

        // Validate input
        $rules = [
            'name' => 'required|min_length[3]|max_length[255]',
            'username' => 'required|min_length[3]|max_length[255]|is_unique[dakoii_users.username,id,' . $id . ']',
            'role' => 'required|in_list[user,moderator,admin]'
        ];

        // Add password validation only if password is provided
        if (!empty($this->request->getPost('password'))) {
            $rules['password'] = 'min_length[6]';
        }

        if (!$this->validate($rules)) {
            session()->setFlashdata('error', 'Please enter valid portal user data. Username must be unique.');
            return redirect()->to('dakoii/portal-users/edit/' . $id)->withInput();
        }

        $data = [
            'name' => trim($this->request->getPost('name')),
            'username' => trim($this->request->getPost('username')),
            'role' => $this->request->getPost('role'),
            'is_active' => $this->request->getPost('is_active') ? 1 : 0
        ];

        // Add password to update data only if provided
        if (!empty($this->request->getPost('password'))) {
            $data['password'] = $this->request->getPost('password'); // Will be hashed by model
        }

        if ($this->portalUsersModel->updatePortalUser($id, $data)) {
            session()->setFlashdata('success', 'Portal user "' . $data['name'] . '" updated successfully!');
            return redirect()->to('dakoii/portal-users');
        } else {
            session()->setFlashdata('error', 'Failed to update portal user. Username may already exist.');
            return redirect()->to('dakoii/portal-users/edit/' . $id)->withInput();
        }
    }

    /**
     * Delete portal user (soft delete by setting is_active to 0)
     */
    public function delete($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/portal-users');
        }

        $portalUser = $this->portalUsersModel->find($id);
        if (!$portalUser) {
            session()->setFlashdata('error', 'Portal user not found');
            return redirect()->to('dakoii/portal-users');
        }

        // Prevent deleting the current user
        if ($this->session->get('dakoii_user_id') == $id) {
            session()->setFlashdata('error', 'You cannot delete your own account');
            return redirect()->to('dakoii/portal-users');
        }

        // Soft delete by deactivating the portal user
        if ($this->portalUsersModel->deactivatePortalUser($id)) {
            session()->setFlashdata('success', 'Portal user "' . $portalUser['name'] . '" has been deactivated successfully!');
        } else {
            session()->setFlashdata('error', 'Failed to deactivate portal user. Please try again.');
        }

        return redirect()->to('dakoii/portal-users');
    }

    /**
     * Check if user is authenticated for Dakoii portal
     */
    private function isAuthenticated(): bool
    {
        return $this->session->has('dakoii_logged_in') && $this->session->get('dakoii_logged_in') === true;
    }

    /**
     * Get current user information
     */
    private function getCurrentUser()
    {
        if (!$this->isAuthenticated()) {
            return null;
        }

        return [
            'id' => $this->session->get('dakoii_user_id'),
            'name' => $this->session->get('dakoii_name'),
            'username' => $this->session->get('dakoii_username'),
            'role' => $this->session->get('dakoii_role'),
            'orgcode' => $this->session->get('dakoii_orgcode')
        ];
    }
}
