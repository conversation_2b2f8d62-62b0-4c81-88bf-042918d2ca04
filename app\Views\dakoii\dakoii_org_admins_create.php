<?= $this->extend('templates/dakoii_template') ?>

<?= $this->section('content') ?>

<!-- <PERSON> Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0"><?= esc($title) ?></h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="<?= base_url('dakoii/organizations') ?>">Organizations</a></li>
                <li class="breadcrumb-item"><a href="<?= base_url('dakoii/organizations/show/' . $org['id']) ?>"><?= esc($org['name']) ?></a></li>
                <li class="breadcrumb-item"><a href="<?= base_url('dakoii/organizations/' . $org['id'] . '/admins') ?>">Administrators</a></li>
                <li class="breadcrumb-item active">Add Administrator</li>
            </ol>
        </nav>
    </div>
    <a href="<?= base_url('dakoii/organizations/' . $org['id'] . '/admins') ?>" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left"></i> Back to Administrators
    </a>
</div>

<!-- Organization Info -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-2">
                <?php if (!empty($org['orglogo'])): ?>
                    <img src="<?= esc($org['orglogo']) ?>" alt="Logo" class="img-fluid rounded" style="max-height: 60px;">
                <?php else: ?>
                    <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 60px;">
                        <i class="fas fa-building fa-2x text-muted"></i>
                    </div>
                <?php endif; ?>
            </div>
            <div class="col-md-10">
                <h6 class="mb-1">Adding Administrator for:</h6>
                <h5 class="mb-1"><?= esc($org['name']) ?></h5>
                <p class="text-muted mb-0"><?= esc($org['description']) ?></p>
            </div>
        </div>
    </div>
</div>

<!-- Create Administrator Form -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-user-plus"></i> Administrator Information
        </h5>
    </div>
    <div class="card-body">
        <form action="<?= base_url('dakoii/organizations/' . $org['id'] . '/admins/store') ?>" method="post">
            <?= csrf_field() ?>
            
            <div class="row">
                <!-- Basic Information -->
                <div class="col-md-6">
                    <h6 class="text-muted mb-3">Basic Information</h6>
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" 
                               value="<?= old('name') ?>" placeholder="Enter administrator's full name">
                    </div>

                    <div class="mb-3">
                        <label for="fileno" class="form-label">File Number</label>
                        <input type="text" class="form-control" id="fileno" name="fileno" 
                               value="<?= old('fileno') ?>" placeholder="Enter file number (optional)">
                        <div class="form-text">Employee file number or ID</div>
                    </div>

                    <div class="mb-3">
                        <label for="position" class="form-label">Position/Title</label>
                        <input type="text" class="form-control" id="position" name="position" 
                               value="<?= old('position') ?>" placeholder="Enter job position or title">
                    </div>
                </div>

                <!-- Account Information -->
                <div class="col-md-6">
                    <h6 class="text-muted mb-3">Account Information</h6>
                    
                    <div class="mb-3">
                        <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="username" name="username" 
                               value="<?= old('username') ?>" placeholder="Enter username">
                        <div class="form-text">Must be unique across the system</div>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                        <input type="password" class="form-control" id="password" name="password" 
                               placeholder="Enter password">
                        <div class="form-text">Minimum 6 characters</div>
                    </div>

                    <div class="mb-3">
                        <label for="status" class="form-label">Account Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="1" <?= old('status', '1') == '1' ? 'selected' : '' ?>>Active</option>
                            <option value="0" <?= old('status') == '0' ? 'selected' : '' ?>>Inactive</option>
                        </select>
                        <div class="form-text">Set initial account status</div>
                    </div>
                </div>
            </div>

            <hr>

            <!-- Contact Information -->
            <div class="row">
                <div class="col-12">
                    <h6 class="text-muted mb-3">Contact Information (Optional)</h6>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="phone" class="form-label">Phone Number</label>
                        <input type="text" class="form-control" id="phone" name="phone" 
                               value="<?= old('phone') ?>" placeholder="Enter phone number">
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" class="form-control" id="email" name="email" 
                               value="<?= old('email') ?>" placeholder="Enter email address">
                    </div>
                </div>
            </div>

            <hr>

            <!-- Form Actions -->
            <div class="d-flex justify-content-between">
                <div>
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i> 
                        Fields marked with <span class="text-danger">*</span> are required
                    </small>
                </div>
                <div class="btn-group">
                    <a href="<?= base_url('dakoii/organizations/' . $org['id'] . '/admins') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Create Administrator
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on name field
    document.getElementById('name').focus();
    
    // Generate username suggestion based on name
    const nameField = document.getElementById('name');
    const usernameField = document.getElementById('username');
    
    nameField.addEventListener('input', function() {
        if (!usernameField.value) {
            // Generate simple username suggestion
            const name = this.value.toLowerCase().replace(/[^a-z0-9]/g, '');
            if (name.length > 0) {
                usernameField.value = name.substring(0, 15);
            }
        }
    });
});
</script>

<?= $this->endSection() ?>
