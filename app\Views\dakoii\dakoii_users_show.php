<?= $this->extend("templates/dakoii_template"); ?>
<?= $this->section('content'); ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-0">User Details</h2>
        <p class="text-muted mb-0">View user information and permissions</p>
    </div>
    <div>
        <a href="<?= base_url('dakoii/users') ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Users
        </a>
        <a href="<?= base_url('dakoii/users/edit/' . $user['id']) ?>" class="btn btn-primary">
            <i class="fas fa-edit"></i> Edit User
        </a>
    </div>
</div>

<div class="row">
    <!-- User Information Card -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user"></i> User Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Full Name</label>
                            <p class="form-control-plaintext"><?= esc($user['name']) ?></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Username</label>
                            <p class="form-control-plaintext">
                                <code class="bg-light px-2 py-1 rounded">@<?= esc($user['username']) ?></code>
                            </p>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Role</label>
                            <p class="form-control-plaintext">
                                <span class="badge bg-<?= $user['role'] == 'admin' ? 'danger' : ($user['role'] == 'moderator' ? 'warning' : 'info') ?> fs-6">
                                    <i class="fas fa-<?= $user['role'] == 'admin' ? 'user-shield' : ($user['role'] == 'moderator' ? 'user-cog' : 'user') ?>"></i>
                                    <?= ucfirst($user['role']) ?>
                                </span>
                            </p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Status</label>
                            <p class="form-control-plaintext">
                                <span class="badge bg-<?= $user['is_active'] ? 'success' : 'danger' ?> fs-6">
                                    <i class="fas fa-<?= $user['is_active'] ? 'check' : 'times' ?>"></i>
                                    <?= $user['is_active'] ? 'Active' : 'Inactive' ?>
                                </span>
                            </p>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">User ID</label>
                            <p class="form-control-plaintext">
                                <code class="bg-light px-2 py-1 rounded">#<?= $user['id'] ?></code>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>

    <!-- User Profile Card -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-circle"></i> User Profile
                </h5>
            </div>
            <div class="card-body text-center">
                <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                     style="width: 100px; height: 100px;">
                    <span class="text-white fw-bold" style="font-size: 2.5rem;">
                        <?= strtoupper(substr($user['name'], 0, 1)) ?>
                    </span>
                </div>
                <h4 class="mb-1"><?= esc($user['name']) ?></h4>
                <p class="text-muted mb-3">@<?= esc($user['username']) ?></p>
                
                <div class="d-grid gap-2">
                    <a href="<?= base_url('dakoii/users/edit/' . $user['id']) ?>" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Edit User
                    </a>
                    <?php if (session()->get('dakoii_user_id') != $user['id']): ?>
                        <button type="button" class="btn btn-outline-danger" 
                                onclick="confirmDelete(<?= $user['id'] ?>, '<?= esc($user['name']) ?>')">
                            <i class="fas fa-user-slash"></i> Deactivate User
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Quick Stats Card -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar"></i> Quick Stats
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>Account Status:</span>
                    <span class="badge bg-<?= $user['is_active'] ? 'success' : 'danger' ?>">
                        <?= $user['is_active'] ? 'Active' : 'Inactive' ?>
                    </span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>Role Level:</span>
                    <span class="badge bg-<?= $user['role'] == 'admin' ? 'danger' : ($user['role'] == 'moderator' ? 'warning' : 'info') ?>">
                        <?= ucfirst($user['role']) ?>
                    </span>
                </div>

            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-warning"></i> Confirm Deactivation
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to deactivate user <strong id="deleteUserName"></strong>?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-info-circle"></i>
                    This will disable the user's access to the system. The user account will not be permanently deleted.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?= csrf_field() ?>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-user-slash"></i> Deactivate User
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.form-control-plaintext {
    padding-left: 0;
    padding-right: 0;
    border: none;
    background: none;
}

.badge.fs-6 {
    font-size: 0.875rem !important;
}
</style>

<script>
function confirmDelete(userId, userName) {
    document.getElementById('deleteUserName').textContent = userName;
    document.getElementById('deleteForm').action = '<?= base_url('dakoii/users/delete/') ?>' + userId;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>

<?= $this->endSection() ?>
