<?= $this->extend("templates/dakoii_template"); ?>
<?= $this->section('content'); ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/system-admins') ?>">System Administrators</a></li>
        <li class="breadcrumb-item active"><?= esc($admin['name']) ?></li>
    </ol>
</nav>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-0"><?= esc($admin['name']) ?></h2>
        <p class="text-muted mb-0">System Administrator Details</p>
    </div>
    <div class="btn-group">
        <a href="<?= base_url('dakoii/system-admins') ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to List
        </a>
        <a href="<?= base_url('dakoii/system-admins/edit/' . $admin['id']) ?>" class="btn btn-primary">
            <i class="fas fa-edit"></i> Edit Administrator
        </a>
    </div>
</div>

<!-- Flash Messages -->
<?php if (session()->getFlashdata('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle"></i> <?= session()->getFlashdata('success') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row">
    <!-- Administrator Details -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-shield"></i> Administrator Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Photo Section -->
                    <div class="col-md-3 text-center mb-4">
                        <?php if (!empty($admin['id_photo'])): ?>
                            <img src="<?= imgcheck($admin['id_photo']) ?>" alt="Administrator Photo"
                                 class="img-fluid rounded shadow" style="max-height: 200px;">
                        <?php else: ?>
                            <div class="bg-primary rounded d-flex align-items-center justify-content-center shadow"
                                 style="height: 200px;">
                                <i class="fas fa-user fa-4x text-white"></i>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Details Section -->
                    <div class="col-md-9">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-medium text-muted" style="width: 30%;">Full Name:</td>
                                <td><?= esc($admin['name']) ?></td>
                            </tr>
                            <tr>
                                <td class="fw-medium text-muted">Username:</td>
                                <td><code class="bg-light px-2 py-1 rounded"><?= esc($admin['username']) ?></code></td>
                            </tr>
                            <?php if (!empty($admin['fileno'])): ?>
                            <tr>
                                <td class="fw-medium text-muted">File Number:</td>
                                <td><?= esc($admin['fileno']) ?></td>
                            </tr>
                            <?php endif; ?>
                            <tr>
                                <td class="fw-medium text-muted">Role:</td>
                                <td>
                                    <span class="badge bg-info fs-6">
                                        <i class="fas fa-user-shield"></i> <?= ucfirst(esc($admin['role'])) ?>
                                    </span>
                                </td>
                            </tr>
                            <?php if (!empty($admin['position'])): ?>
                            <tr>
                                <td class="fw-medium text-muted">Position:</td>
                                <td><?= esc($admin['position']) ?></td>
                            </tr>
                            <?php endif; ?>
                            <tr>
                                <td class="fw-medium text-muted">Organization:</td>
                                <td>
                                    <div class="fw-medium"><?= esc($admin['org_name'] ?? 'Unknown Organization') ?></div>
                                    <small class="text-muted">Code: <?= esc($admin['orgcode']) ?></small>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-medium text-muted">Account Status:</td>
                                <td>
                                    <?php
                                    $statusValue = isset($admin['status']) ? $admin['status'] : 0;
                                    $isActive = $statusValue == 1;
                                    ?>
                                    <span class="badge bg-<?= $isActive ? 'success' : 'danger' ?> fs-6">
                                        <i class="fas fa-<?= $isActive ? 'check' : 'times' ?>"></i>
                                        <?= $isActive ? 'Active' : 'Inactive' ?>
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact & Additional Info -->
    <div class="col-lg-4 mb-4">
        <!-- Contact Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-address-book"></i> Contact Information
                </h6>
            </div>
            <div class="card-body">
                <?php if (!empty($admin['email'])): ?>
                    <div class="mb-3">
                        <div class="fw-medium text-muted">Email Address</div>
                        <div>
                            <i class="fas fa-envelope text-muted me-2"></i>
                            <a href="mailto:<?= esc($admin['email']) ?>"><?= esc($admin['email']) ?></a>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (!empty($admin['phone'])): ?>
                    <div class="mb-3">
                        <div class="fw-medium text-muted">Phone Number</div>
                        <div>
                            <i class="fas fa-phone text-muted me-2"></i>
                            <a href="tel:<?= esc($admin['phone']) ?>"><?= esc($admin['phone']) ?></a>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (empty($admin['email']) && empty($admin['phone'])): ?>
                    <div class="text-center py-3">
                        <i class="fas fa-address-book fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">No contact information available</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Account Information -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i> Account Information
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="fw-medium text-muted">Created</div>
                    <div><?= date('F j, Y \a\t g:i A', strtotime($admin['created_at'])) ?></div>
                </div>

                <?php if (!empty($admin['created_by'])): ?>
                <div class="mb-3">
                    <div class="fw-medium text-muted">Created By</div>
                    <div><?= esc($admin['created_by']) ?></div>
                </div>
                <?php endif; ?>

                <div class="mb-3">
                    <div class="fw-medium text-muted">Last Updated</div>
                    <div><?= date('F j, Y \a\t g:i A', strtotime($admin['updated_at'])) ?></div>
                </div>

                <?php if (!empty($admin['updated_by'])): ?>
                <div class="mb-3">
                    <div class="fw-medium text-muted">Updated By</div>
                    <div><?= esc($admin['updated_by']) ?></div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-cogs"></i> Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-flex gap-2 flex-wrap">
                    <a href="<?= base_url('dakoii/system-admins/edit/' . $admin['id']) ?>" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Edit Administrator
                    </a>

                    <button type="button" class="btn btn-danger"
                            onclick="confirmDelete(<?= $admin['id'] ?>, '<?= esc($admin['name']) ?>')">
                        <i class="fas fa-trash"></i> Delete Administrator
                    </button>

                    <a href="<?= base_url('dakoii/organizations/show/' . $admin['org_table_id']) ?>" class="btn btn-outline-info">
                        <i class="fas fa-building"></i> View Organization
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-warning"></i> Confirm Delete
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the system administrator <strong id="adminName"></strong>?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Warning:</strong> This action cannot be undone. The administrator will lose access to the system.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    <?= csrf_field() ?>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Delete Administrator
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(adminId, adminName) {
    document.getElementById('adminName').textContent = adminName;
    document.getElementById('deleteForm').action = '<?= base_url('dakoii/system-admins/delete/') ?>' + adminId;

    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>

<?= $this->endSection() ?>
